// LinkedIn Search Floating UI
class LinkedInSearchFloatingUI {
    constructor() {
        this.ui = null;
        this.isCollecting = false;
        this.collectedProfiles = [];
        this.currentStep = 'filters'; // 'filters', 'collecting', 'processing'
        this.init();
    }

    init() {
        this.injectCSS();
        this.createUI();
        this.setupEventListeners();
        this.showUI();
    }

    injectCSS() {
        if (document.getElementById('linkedin-search-ui-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'linkedin-search-ui-styles';
        style.textContent = `
            .linkedin-search-floating-ui {
                position: fixed !important;
                top: 80px !important;
                right: 20px !important;
                width: 420px !important;
                max-height: 80vh !important;
                background: white !important;
                border-radius: 12px !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                z-index: 999999 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                border: 1px solid #e1e5e9 !important;
                overflow: hidden !important;
                display: flex !important;
                flex-direction: column !important;
                cursor: move !important;
            }

            .linkedin-search-header {
                background: linear-gradient(135deg, #0a66c2, #004182) !important;
                color: white !important;
                padding: 16px 20px !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                border-radius: 12px 12px 0 0 !important;
                cursor: move !important;
                user-select: none !important;
            }

            .linkedin-search-title {
                font-size: 16px !important;
                font-weight: 600 !important;
                margin: 0 !important;
            }

            .linkedin-search-controls {
                display: flex !important;
                gap: 12px !important;
                margin-bottom: 20px !important;
            }

            .linkedin-search-minimize,
            .linkedin-search-close {
                background: rgba(255, 255, 255, 0.2) !important;
                border: none !important;
                color: white !important;
                width: 28px !important;
                height: 28px !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 16px !important;
                transition: background-color 0.2s ease !important;
            }

            .linkedin-search-minimize:hover,
            .linkedin-search-close:hover {
                background: rgba(255, 255, 255, 0.3) !important;
            }

            .linkedin-search-content {
                padding: 20px !important;
                overflow-y: auto !important;
                flex: 1 !important;
            }

            .linkedin-search-btn {
                flex: 1 !important;
                padding: 12px 16px !important;
                border: none !important;
                border-radius: 8px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
            }

            .linkedin-search-btn.start {
                background: #28a745 !important;
                color: white !important;
            }

            .linkedin-search-btn.start:hover {
                background: #218838 !important;
                transform: translateY(-1px) !important;
            }

            .linkedin-search-btn.pause {
                background: #ffc107 !important;
                color: #212529 !important;
            }

            .linkedin-search-btn.pause:hover {
                background: #e0a800 !important;
                transform: translateY(-1px) !important;
            }

            .linkedin-search-btn:disabled {
                opacity: 0.6 !important;
                cursor: not-allowed !important;
                transform: none !important;
            }

            .linkedin-search-status {
                background: #f8f9fa !important;
                border: 1px solid #e9ecef !important;
                border-radius: 8px !important;
                padding: 12px 16px !important;
                margin-bottom: 20px !important;
                text-align: center !important;
            }

            .status-indicator {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                gap: 8px !important;
            }

            .status-dot {
                width: 8px !important;
                height: 8px !important;
                border-radius: 50% !important;
                background: #6c757d !important;
            }

            .status-dot.active {
                background: #28a745 !important;
                animation: pulse 2s infinite !important;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }

            .connection-stats {
                display: flex !important;
                gap: 12px !important;
                margin-bottom: 20px !important;
            }

            .stat-card {
                flex: 1 !important;
                padding: 16px !important;
                border-radius: 8px !important;
                text-align: center !important;
            }

            .stat-card.send-connect {
                background: #d4edda !important;
                border: 1px solid #c3e6cb !important;
            }

            .stat-card.field-connect {
                background: #fff3cd !important;
                border: 1px solid #ffeaa7 !important;
            }

            .stat-label {
                font-size: 12px !important;
                color: #6c757d !important;
                margin-bottom: 4px !important;
            }

            .stat-number {
                font-size: 24px !important;
                font-weight: 700 !important;
            }

            .send-connect .stat-number {
                color: #28a745 !important;
            }

            .field-connect .stat-number {
                color: #ffc107 !important;
            }

            .profiles-section {
                margin-bottom: 20px !important;
            }

            .profiles-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                margin-bottom: 12px !important;
            }

            .profiles-count {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: #333 !important;
            }

            .clear-btn {
                background: none !important;
                border: none !important;
                color: #dc3545 !important;
                font-size: 12px !important;
                cursor: pointer !important;
                text-decoration: underline !important;
            }

            .clear-btn:hover {
                color: #c82333 !important;
            }

            .profiles-list {
                background: #f8f9fa !important;
                border: 1px solid #e9ecef !important;
                border-radius: 8px !important;
                padding: 16px !important;
                min-height: 100px !important;
                max-height: 200px !important;
                overflow-y: auto !important;
            }

            .empty-profiles {
                text-align: center !important;
                color: #6c757d !important;
                font-size: 14px !important;
                font-style: italic !important;
            }

            .linkedin-search-btn.next {
                background: #007bff !important;
                color: white !important;
                margin-top: 12px !important;
                width: 100% !important;
            }

            .linkedin-search-btn.next:hover {
                background: #0056b3 !important;
            }

            .linkedin-search-btn.next:disabled {
                background: #6c757d !important;
                cursor: not-allowed !important;
            }
        `;
        document.head.appendChild(style);
    }

    createUI() {
        this.ui = document.createElement('div');
        this.ui.className = 'linkedin-search-floating-ui';
        this.ui.innerHTML = `
            <div class="linkedin-search-header">
                <h3 class="linkedin-search-title">LinkedIn Search</h3>
                <div class="linkedin-search-controls">
                    <button class="linkedin-search-minimize" title="Minimize">−</button>
                    <button class="linkedin-search-close" title="Close">&times;</button>
                </div>
            </div>
            <div class="linkedin-search-content">
                <div class="linkedin-search-controls">
                    <button class="linkedin-search-btn start" id="show-filters-btn">SHOW LINKEDIN FILTERS</button>
                    <button class="linkedin-search-btn pause" id="collect-profiles-btn" disabled>COLLECT PROFILES</button>
                </div>
                <div class="linkedin-search-status">
                    <div class="status-indicator">
                        <span class="status-dot" id="status-dot"></span>
                        <span id="status-text">Ready to show LinkedIn filters</span>
                    </div>
                </div>

                <div class="connection-stats">
                    <div class="stat-card send-connect">
                        <div class="stat-label">Send Connect:</div>
                        <div class="stat-number" id="send-connect-count">0</div>
                    </div>
                    <div class="stat-card field-connect">
                        <div class="stat-label">Field Connect:</div>
                        <div class="stat-number" id="field-connect-count">0</div>
                    </div>
                </div>

                <div class="profiles-section">
                    <div class="profiles-header">
                        <span class="profiles-count">Profiles: <span id="profile-count">0</span></span>
                        <button class="clear-btn" id="clear-profiles">Clear All</button>
                    </div>
                    <div class="profiles-list" id="profiles-list">
                        <div class="empty-profiles">
                            No profiles collected yet. Click "Show LinkedIn Filters" to begin.
                        </div>
                    </div>
                </div>

                <button class="linkedin-search-btn next" id="start-connecting-btn" style="display: none;">
                    Next: Start Connecting (0)
                </button>
            </div>
        `;
        document.body.appendChild(this.ui);
    }

    setupEventListeners() {
        const showFiltersBtn = this.ui.querySelector('#show-filters-btn');
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        const connectBtn = this.ui.querySelector('#start-connecting-btn');
        const clearBtn = this.ui.querySelector('#clear-profiles');
        const closeBtn = this.ui.querySelector('.linkedin-search-close');
        const minimizeBtn = this.ui.querySelector('.linkedin-search-minimize');
        const header = this.ui.querySelector('.linkedin-search-header');

        showFiltersBtn.addEventListener('click', () => this.showLinkedInFilters());
        collectBtn.addEventListener('click', () => this.collectProfiles());
        connectBtn.addEventListener('click', () => this.startConnecting());
        clearBtn.addEventListener('click', () => this.clearProfiles());
        closeBtn.addEventListener('click', () => this.closeUI());
        minimizeBtn.addEventListener('click', () => this.toggleMinimize());

        this.makeDraggable(header);
    }

    makeDraggable(handle) {
        let isDragging = false;
        let currentX, currentY, initialX, initialY;
        let xOffset = 0, yOffset = 0;

        handle.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            if (e.target === handle || handle.contains(e.target)) {
                isDragging = true;
                this.ui.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                xOffset = currentX;
                yOffset = currentY;
                this.ui.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            this.ui.style.cursor = 'move';
        });
    }

    showLinkedInFilters() {
        this.updateStatus('status', 'Opening LinkedIn search filters...', true);

        // Enable collect button
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        collectBtn.disabled = false;
        collectBtn.textContent = 'COLLECT PROFILES';

        // Navigate to LinkedIn search with filters
        window.location.href = 'https://www.linkedin.com/search/results/people/?origin=FACETED_SEARCH';

        setTimeout(() => {
            this.updateStatus('status', 'LinkedIn filters opened. Now click "COLLECT PROFILES"', false);
        }, 2000);
    }

    collectProfiles() {
        this.updateStatus('status', 'Collecting profiles...', true);
        this.isCollecting = true;

        // Update button states
        const showFiltersBtn = this.ui.querySelector('#show-filters-btn');
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');

        showFiltersBtn.disabled = true;
        collectBtn.textContent = 'COLLECTING...';
        collectBtn.disabled = true;

        // Start profile collection logic here
        this.startProfileCollection();
    }

    startProfileCollection() {
        // Profile collection implementation
        const profiles = document.querySelectorAll('[data-chameleon-result-urn], .reusable-search__result-container, .entity-result');

        profiles.forEach((profile, index) => {
            setTimeout(() => {
                this.processProfile(profile);
                this.updateProfileCount();
            }, index * 500);
        });

        // Show next button after collection
        setTimeout(() => {
            this.showNextButton();
        }, profiles.length * 500 + 1000);
    }

    processProfile(profileElement) {
        // Extract profile data
        const profileData = {
            name: profileElement.querySelector('.entity-result__title-text a, .actor-name')?.textContent?.trim() || 'Unknown',
            url: profileElement.querySelector('.entity-result__title-text a, .actor-name a')?.href || '',
            title: profileElement.querySelector('.entity-result__primary-subtitle, .actor-occupation')?.textContent?.trim() || '',
            location: profileElement.querySelector('.entity-result__secondary-subtitle, .actor-meta')?.textContent?.trim() || ''
        };

        if (profileData.url) {
            this.collectedProfiles.push(profileData);
            this.updateProfilesList();
        }
    }

    updateProfileCount() {
        const countElement = this.ui.querySelector('#profile-count');
        countElement.textContent = this.collectedProfiles.length;

        // Update next button
        const nextBtn = this.ui.querySelector('#start-connecting-btn');
        nextBtn.textContent = `Next: Start Connecting (${this.collectedProfiles.length})`;
    }

    updateProfilesList() {
        const profilesList = this.ui.querySelector('#profiles-list');

        if (this.collectedProfiles.length === 0) {
            profilesList.innerHTML = '<div class="empty-profiles">No profiles collected yet. Click "Show LinkedIn Filters" to begin.</div>';
        } else {
            profilesList.innerHTML = this.collectedProfiles.map((profile) => `
                <div class="profile-item" style="padding: 8px 0; border-bottom: 1px solid #e9ecef; font-size: 12px;">
                    <strong>${profile.name}</strong><br>
                    <span style="color: #6c757d;">${profile.title}</span>
                </div>
            `).join('');
        }
    }

    showNextButton() {
        const nextBtn = this.ui.querySelector('#start-connecting-btn');
        nextBtn.style.display = 'block';
        nextBtn.disabled = this.collectedProfiles.length === 0;

        this.updateStatus('status', `Collected ${this.collectedProfiles.length} profiles. Ready to connect!`, false);
    }

    startConnecting() {
        this.updateStatus('status', 'Starting connection requests...', true);

        // Implementation for sending connection requests
        this.processConnectionRequests();
    }

    processConnectionRequests() {
        // Connection request logic will be implemented here
        console.log('Processing connection requests for:', this.collectedProfiles);

        // Update stats
        const sendConnectCount = this.ui.querySelector('#send-connect-count');
        const fieldConnectCount = this.ui.querySelector('#field-connect-count');

        sendConnectCount.textContent = Math.floor(this.collectedProfiles.length * 0.7);
        fieldConnectCount.textContent = Math.floor(this.collectedProfiles.length * 0.3);
    }

    clearProfiles() {
        this.collectedProfiles = [];
        this.updateProfileCount();
        this.updateProfilesList();

        // Reset UI state
        const showFiltersBtn = this.ui.querySelector('#show-filters-btn');
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        const nextBtn = this.ui.querySelector('#start-connecting-btn');

        showFiltersBtn.disabled = false;
        collectBtn.disabled = true;
        collectBtn.textContent = 'COLLECT PROFILES';
        nextBtn.style.display = 'none';

        // Reset stats
        this.ui.querySelector('#send-connect-count').textContent = '0';
        this.ui.querySelector('#field-connect-count').textContent = '0';

        this.updateStatus('status', 'Ready to show LinkedIn filters', false);
    }

    updateStatus(statusType, message, isActive = false) {
        const statusText = this.ui.querySelector(`#${statusType}-text`);
        const statusDot = this.ui.querySelector(`#${statusType}-dot`);

        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.classList.toggle('active', isActive);
        }
    }

    showUI() {
        if (this.ui) {
            this.ui.style.display = 'flex';
            this.ui.style.visibility = 'visible';
            this.ui.style.opacity = '1';
        }
    }

    closeUI() {
        if (this.ui) {
            this.ui.remove();
        }
    }

    toggleMinimize() {
        const content = this.ui.querySelector('.linkedin-search-content');
        if (content.style.display === 'none') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    }
}

// Auto-initialize when on LinkedIn search pages
function initLinkedInSearchUI() {
    if (window.location.href.includes('linkedin.com/search') &&
        !document.querySelector('.linkedin-search-floating-ui') &&
        !window['linkedInSearchUI']) {

        // Wait for page to load
        setTimeout(() => {
            window['linkedInSearchUI'] = new LinkedInSearchFloatingUI();
        }, 2000);
    }
}

// Initialize on page load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLinkedInSearchUI);
} else {
    initLinkedInSearchUI();
}

// Re-initialize on navigation changes
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(initLinkedInSearchUI, 1000);
    }
}).observe(document, { subtree: true, childList: true });

window.LinkedInSearchFloatingUI = LinkedInSearchFloatingUI;
