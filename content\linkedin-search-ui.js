// LinkedIn Search Floating UI
class LinkedInSearchFloatingUI {
    constructor() {
        this.ui = null;
        this.isCollecting = false;
        this.collectedProfiles = [];
        this.currentStep = 'filters'; // 'filters', 'collecting', 'processing'
        this.init();
    }

    init() {
        this.injectCSS();
        this.createUI();
        this.setupEventListeners();
        this.showUI();
    }

    injectCSS() {
        if (document.getElementById('linkedin-search-ui-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'linkedin-search-ui-styles';
        style.textContent = `
            .linkedin-search-floating-ui {
                position: fixed !important;
                top: 80px !important;
                right: 20px !important;
                width: 420px !important;
                max-height: 80vh !important;
                background: white !important;
                border-radius: 12px !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                z-index: 999999 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                border: 1px solid #e1e5e9 !important;
                overflow: hidden !important;
                display: flex !important;
                flex-direction: column !important;
                cursor: move !important;
            }

            .linkedin-search-header {
                background: linear-gradient(135deg, #0077b5, #005885) !important;
                color: white !important;
                padding: 16px 20px !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                border-radius: 12px 12px 0 0 !important;
                cursor: move !important;
                user-select: none !important;
            }

            .linkedin-search-title {
                font-size: 16px !important;
                font-weight: 600 !important;
                margin: 0 !important;
            }

            .linkedin-search-controls {
                display: flex !important;
                gap: 8px !important;
                align-items: center !important;
            }

            .linkedin-search-minimize,
            .linkedin-search-close {
                background: rgba(255, 255, 255, 0.2) !important;
                border: none !important;
                color: white !important;
                width: 28px !important;
                height: 28px !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 16px !important;
                transition: background-color 0.2s ease !important;
            }

            .linkedin-search-minimize:hover,
            .linkedin-search-close:hover {
                background: rgba(255, 255, 255, 0.3) !important;
            }

            .linkedin-search-content {
                padding: 20px !important;
                overflow-y: auto !important;
                flex: 1 !important;
            }

            .linkedin-search-btn {
                width: 100% !important;
                padding: 12px 16px !important;
                border: none !important;
                border-radius: 8px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
                margin-bottom: 12px !important;
            }

            .linkedin-search-btn.primary {
                background: #0077b5 !important;
                color: white !important;
            }

            .linkedin-search-btn.primary:hover {
                background: #005885 !important;
                transform: translateY(-1px) !important;
            }

            .linkedin-search-btn.secondary {
                background: #28a745 !important;
                color: white !important;
            }

            .linkedin-search-btn.secondary:hover {
                background: #218838 !important;
                transform: translateY(-1px) !important;
            }

            .linkedin-search-status {
                background: #f8f9fa !important;
                border: 1px solid #e9ecef !important;
                border-radius: 8px !important;
                padding: 12px 16px !important;
                margin-bottom: 20px !important;
                text-align: center !important;
            }

            .status-indicator {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                gap: 8px !important;
            }

            .status-dot {
                width: 8px !important;
                height: 8px !important;
                border-radius: 50% !important;
                background: #6c757d !important;
            }

            .status-dot.active {
                background: #28a745 !important;
                animation: pulse 2s infinite !important;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }

            .profile-count {
                font-size: 18px !important;
                font-weight: 600 !important;
                color: #0077b5 !important;
                text-align: center !important;
                margin: 15px 0 !important;
            }

            .step-indicator {
                display: flex !important;
                justify-content: space-between !important;
                margin-bottom: 20px !important;
                padding: 0 10px !important;
            }

            .step {
                flex: 1 !important;
                text-align: center !important;
                padding: 8px !important;
                border-radius: 6px !important;
                font-size: 12px !important;
                font-weight: 500 !important;
                background: #f8f9fa !important;
                color: #6c757d !important;
                margin: 0 2px !important;
            }

            .step.active {
                background: #0077b5 !important;
                color: white !important;
            }

            .step.completed {
                background: #28a745 !important;
                color: white !important;
            }
        `;
        document.head.appendChild(style);
    }

    createUI() {
        this.ui = document.createElement('div');
        this.ui.className = 'linkedin-search-floating-ui';
        this.ui.innerHTML = `
            <div class="linkedin-search-header">
                <h3 class="linkedin-search-title">LinkedIn Search</h3>
                <div class="linkedin-search-controls">
                    <button class="linkedin-search-minimize" title="Minimize">−</button>
                    <button class="linkedin-search-close" title="Close">&times;</button>
                </div>
            </div>
            <div class="linkedin-search-content">
                <div class="step-indicator">
                    <div class="step active" id="step-filters">1. Filters</div>
                    <div class="step" id="step-collect">2. Collect</div>
                    <div class="step" id="step-connect">3. Connect</div>
                </div>
                
                <div id="filters-step" class="step-content">
                    <button class="linkedin-search-btn primary" id="show-filters-btn">SHOW LINKEDIN FILTERS</button>
                    <div class="linkedin-search-status">
                        <div class="status-indicator">
                            <span class="status-dot" id="status-dot"></span>
                            <span id="status-text">Ready to show LinkedIn filters</span>
                        </div>
                    </div>
                </div>

                <div id="collect-step" class="step-content" style="display: none;">
                    <button class="linkedin-search-btn secondary" id="collect-profiles-btn">COLLECT PROFILES</button>
                    <div class="profile-count" id="profile-count">0 profiles collected</div>
                    <div class="linkedin-search-status">
                        <div class="status-indicator">
                            <span class="status-dot" id="collect-status-dot"></span>
                            <span id="collect-status-text">Ready to collect profiles</span>
                        </div>
                    </div>
                </div>

                <div id="connect-step" class="step-content" style="display: none;">
                    <button class="linkedin-search-btn primary" id="start-connecting-btn">START CONNECTING</button>
                    <div class="linkedin-search-status">
                        <div class="status-indicator">
                            <span class="status-dot" id="connect-status-dot"></span>
                            <span id="connect-status-text">Ready to send connection requests</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(this.ui);
    }

    setupEventListeners() {
        const showFiltersBtn = this.ui.querySelector('#show-filters-btn');
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        const connectBtn = this.ui.querySelector('#start-connecting-btn');
        const closeBtn = this.ui.querySelector('.linkedin-search-close');
        const minimizeBtn = this.ui.querySelector('.linkedin-search-minimize');
        const header = this.ui.querySelector('.linkedin-search-header');

        showFiltersBtn.addEventListener('click', () => this.showLinkedInFilters());
        collectBtn.addEventListener('click', () => this.collectProfiles());
        connectBtn.addEventListener('click', () => this.startConnecting());
        closeBtn.addEventListener('click', () => this.closeUI());
        minimizeBtn.addEventListener('click', () => this.toggleMinimize());
        
        this.makeDraggable(header);
    }

    makeDraggable(handle) {
        let isDragging = false;
        let currentX, currentY, initialX, initialY;
        let xOffset = 0, yOffset = 0;

        handle.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            if (e.target === handle || handle.contains(e.target)) {
                isDragging = true;
                this.ui.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                xOffset = currentX;
                yOffset = currentY;
                this.ui.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            this.ui.style.cursor = 'move';
        });
    }

    showLinkedInFilters() {
        this.updateStatus('status', 'Opening LinkedIn search filters...', true);
        
        // Navigate to LinkedIn search with filters
        window.location.href = 'https://www.linkedin.com/search/results/people/?origin=FACETED_SEARCH';
        
        setTimeout(() => {
            this.moveToStep('collect');
        }, 2000);
    }

    collectProfiles() {
        this.updateStatus('collect-status', 'Collecting profiles...', true);
        this.isCollecting = true;
        
        // Start profile collection logic here
        this.startProfileCollection();
    }

    startProfileCollection() {
        // Profile collection implementation
        const profiles = document.querySelectorAll('[data-chameleon-result-urn], .reusable-search__result-container');
        
        profiles.forEach((profile, index) => {
            setTimeout(() => {
                this.processProfile(profile);
                this.updateProfileCount();
            }, index * 500);
        });
    }

    processProfile(profileElement) {
        // Extract profile data
        const profileData = {
            name: profileElement.querySelector('.entity-result__title-text a')?.textContent?.trim() || 'Unknown',
            url: profileElement.querySelector('.entity-result__title-text a')?.href || '',
            title: profileElement.querySelector('.entity-result__primary-subtitle')?.textContent?.trim() || '',
            location: profileElement.querySelector('.entity-result__secondary-subtitle')?.textContent?.trim() || ''
        };
        
        if (profileData.url) {
            this.collectedProfiles.push(profileData);
        }
    }

    updateProfileCount() {
        const countElement = this.ui.querySelector('#profile-count');
        countElement.textContent = `${this.collectedProfiles.length} profiles collected`;
        
        if (this.collectedProfiles.length > 0) {
            setTimeout(() => {
                this.moveToStep('connect');
            }, 1000);
        }
    }

    startConnecting() {
        this.updateStatus('connect-status', 'Starting connection requests...', true);
        
        // Implementation for sending connection requests
        this.processConnectionRequests();
    }

    processConnectionRequests() {
        // Connection request logic will be implemented here
        console.log('Processing connection requests for:', this.collectedProfiles);
    }

    moveToStep(step) {
        // Hide all step contents
        this.ui.querySelectorAll('.step-content').forEach(content => {
            content.style.display = 'none';
        });
        
        // Remove active class from all steps
        this.ui.querySelectorAll('.step').forEach(stepEl => {
            stepEl.classList.remove('active');
            stepEl.classList.add('completed');
        });
        
        // Show current step
        const stepContent = this.ui.querySelector(`#${step}-step`);
        const stepIndicator = this.ui.querySelector(`#step-${step}`);
        
        if (stepContent) stepContent.style.display = 'block';
        if (stepIndicator) {
            stepIndicator.classList.remove('completed');
            stepIndicator.classList.add('active');
        }
        
        this.currentStep = step;
    }

    updateStatus(statusType, message, isActive = false) {
        const statusText = this.ui.querySelector(`#${statusType}-text`);
        const statusDot = this.ui.querySelector(`#${statusType}-dot`);
        
        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.classList.toggle('active', isActive);
        }
    }

    showUI() {
        if (this.ui) {
            this.ui.style.display = 'flex';
            this.ui.style.visibility = 'visible';
            this.ui.style.opacity = '1';
        }
    }

    closeUI() {
        if (this.ui) {
            this.ui.remove();
        }
    }

    toggleMinimize() {
        const content = this.ui.querySelector('.linkedin-search-content');
        if (content.style.display === 'none') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    }
}

// Auto-initialize when on LinkedIn search pages
function initLinkedInSearchUI() {
    if (window.location.href.includes('linkedin.com/search') &&
        !document.querySelector('.linkedin-search-floating-ui') &&
        !window['linkedInSearchUI']) {

        // Wait for page to load
        setTimeout(() => {
            window['linkedInSearchUI'] = new LinkedInSearchFloatingUI();
        }, 2000);
    }
}

// Initialize on page load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLinkedInSearchUI);
} else {
    initLinkedInSearchUI();
}

// Re-initialize on navigation changes
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(initLinkedInSearchUI, 1000);
    }
}).observe(document, { subtree: true, childList: true });

window.LinkedInSearchFloatingUI = LinkedInSearchFloatingUI;
