.linkedin-search-floating-ui {
    position: fixed !important;
    top: 80px !important;
    right: 20px !important;
    width: 420px !important;
    max-height: 80vh !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    z-index: 999999 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    border: 1px solid #e1e5e9 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    cursor: move !important;
}

.linkedin-search-header {
    background: linear-gradient(135deg, #0a66c2, #004182) !important;
    color: white !important;
    padding: 16px 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    border-radius: 12px 12px 0 0 !important;
    cursor: move !important;
    user-select: none !important;
}

.linkedin-search-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.linkedin-search-controls {
    display: flex !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
}

.linkedin-search-minimize,
.linkedin-search-close {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    color: white !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    transition: background-color 0.2s ease !important;
}

.linkedin-search-minimize:hover,
.linkedin-search-close:hover {
    background: rgba(255, 255, 255, 0.3) !important;
}

.linkedin-search-content {
    padding: 20px !important;
    overflow-y: auto !important;
    flex: 1 !important;
}

.linkedin-search-btn {
    flex: 1 !important;
    padding: 12px 16px !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.linkedin-search-btn.start {
    background: #28a745 !important;
    color: white !important;
}

.linkedin-search-btn.start:hover {
    background: #218838 !important;
    transform: translateY(-1px) !important;
}

.linkedin-search-btn.pause {
    background: #ffc107 !important;
    color: #212529 !important;
}

.linkedin-search-btn.pause:hover {
    background: #e0a800 !important;
    transform: translateY(-1px) !important;
}

.linkedin-search-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.linkedin-search-status {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    margin-bottom: 20px !important;
    text-align: center !important;
}

.status-indicator {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

.status-dot {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: #6c757d !important;
}

.status-dot.active {
    background: #28a745 !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.connection-stats {
    display: flex !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
}

.stat-card {
    flex: 1 !important;
    padding: 16px !important;
    border-radius: 8px !important;
    text-align: center !important;
}

.stat-card.send-connect {
    background: #d4edda !important;
    border: 1px solid #c3e6cb !important;
}

.stat-card.field-connect {
    background: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
}

.stat-label {
    font-size: 12px !important;
    color: #6c757d !important;
    margin-bottom: 4px !important;
}

.stat-number {
    font-size: 24px !important;
    font-weight: 700 !important;
}

.send-connect .stat-number {
    color: #28a745 !important;
}

.field-connect .stat-number {
    color: #ffc107 !important;
}

.profiles-section {
    margin-bottom: 20px !important;
}

.profiles-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
}

.profiles-count {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #333 !important;
}

.clear-btn {
    background: none !important;
    border: none !important;
    color: #dc3545 !important;
    font-size: 12px !important;
    cursor: pointer !important;
    text-decoration: underline !important;
}

.clear-btn:hover {
    color: #c82333 !important;
}

.profiles-list {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 16px !important;
    min-height: 100px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
}

.empty-profiles {
    text-align: center !important;
    color: #6c757d !important;
    font-size: 14px !important;
    font-style: italic !important;
}

.profile-item {
    padding: 8px 0 !important;
    border-bottom: 1px solid #e9ecef !important;
    font-size: 12px !important;
}

.profile-item:last-child {
    border-bottom: none !important;
}

.profile-item strong {
    display: block !important;
    margin-bottom: 2px !important;
}

.profile-item span {
    color: #6c757d !important;
}

.linkedin-search-btn.next {
    background: #007bff !important;
    color: white !important;
    margin-top: 12px !important;
    width: 100% !important;
}

.linkedin-search-btn.next:hover {
    background: #0056b3 !important;
}

.linkedin-search-btn.next:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .linkedin-search-floating-ui {
        width: 380px !important;
        right: 10px !important;
        top: 60px !important;
        max-height: 70vh !important;
    }
}

@media (max-width: 480px) {
    .linkedin-search-floating-ui {
        width: 350px !important;
        right: 5px !important;
        top: 50px !important;
        max-height: 60vh !important;
    }
}

/* Dragging states */
.linkedin-search-floating-ui.dragging {
    transition: none !important;
    user-select: none !important;
}

.linkedin-search-floating-ui.minimized .linkedin-search-content {
    display: none !important;
}
